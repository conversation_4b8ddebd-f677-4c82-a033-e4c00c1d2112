{"version": 3, "file": "js/6596.b175c1e3.js", "mappings": "kJAAA,IAAIA,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,OAAO,CAACE,YAAY,gBAAgB,CAACJ,EAAIK,GAAG,WAAWH,EAAG,SAASA,EAAG,UAAUA,EAAG,WAAW,EACpK,EACII,EAAkB,G,8BCctB,GACAC,KAAA,SACAC,WAAA,CAAAC,OAAA,IAAAC,MAAA,IAAAC,OAAAA,EAAAA,IClB+P,I,UCQ3PC,GAAY,OACd,EACAb,EACAO,GACA,EACA,KACA,WACA,MAIF,EAAeM,EAAiB,O,uDCnBhC,IAAIb,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,UAAU,CAACF,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACJ,EAAIa,GAAG,YAAYX,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACJ,EAAIa,GAAG,iBAAiBX,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,IAAI,CAACY,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOf,EAAIgB,WAAW,SAAS,IAAI,CAACd,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAIa,GAAG,UAAUX,EAAG,IAAI,CAACY,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOf,EAAIgB,WAAW,WAAW,IAAI,CAACd,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAIa,GAAG,kBAAkBX,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACJ,EAAIa,GAAG,WAAWX,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAIa,GAAG,UAAUX,EAAG,IAAI,CAACY,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOf,EAAIgB,WAAW,QAAQ,IAAI,CAACd,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAIa,GAAG,YAAYX,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAIa,GAAG,QAAQX,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAIa,GAAG,cAAcb,EAAIiB,GAAG,GAAGjB,EAAIiB,GAAG,GAAGjB,EAAIiB,GAAG,KAAKjB,EAAIiB,GAAG,IACtjC,EACIX,EAAkB,CAAC,WAAY,IAAIN,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACJ,EAAIa,GAAG,YAAYX,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAIa,GAAG,YAAYX,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAIa,GAAG,aAAaX,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAIa,GAAG,YAAYX,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAIa,GAAG,kBACza,EAAE,WAAY,IAAIb,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACJ,EAAIa,GAAG,YAAYX,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACgB,MAAM,CAAC,IAAMC,EAAQ,IAAqC,IAAM,kBACtQ,EAAE,WAAY,IAAInB,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACJ,EAAIa,GAAG,WAAWX,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACgB,MAAM,CAAC,IAAMC,EAAQ,IAAqC,IAAM,iBACrQ,EAAE,WAAY,IAAInB,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAACF,EAAG,IAAI,CAACkB,YAAY,CAAC,MAAQ,UAAU,kBAAkB,QAAQF,MAAM,CAAC,KAAO,4BAA4B,OAAS,WAAW,CAAClB,EAAIa,GAAG,0BAA0Bb,EAAIa,GAAG,8CAC7S,GC2EA,G,QAAA,CACAN,KAAA,SACAc,OACA,OACA,CAEA,EACAC,QAAA,CACAN,WAAAO,GAEA,KAAAC,aAAA,KAAAA,cAAAD,GACA,KAAAE,mBAAA,KAAAD,YAGA,KAAAE,WAAA,KACA,MAAAC,EAAAC,SAAAC,iBAAA,yBACAF,EAAAG,SAAAC,KACAA,EAAAC,UAAAC,SAAA,WACA,WAAAV,GAAAQ,EAAAC,UAAAC,SAAA,gBACAF,EAAAC,UAAAC,SAAA,iBACAF,EAAAC,UAAAE,IAAA,eAGAC,YAAA,KACAJ,EAAAC,UAAAI,OAAA,iBACA,KACA,IAIA,KAAAZ,YAAAD,CAAA,KAGA,KAAAC,YAAAD,EAIA,KAAAc,OAAAd,OAAAA,EACA,KAAAG,WAAA,KACAY,OAAAC,SAAA,CACAC,IAAA,EACAC,SAAA,YAEA,KAAAC,QAAAC,GAAA,OAIA,KAAAD,QAAAE,KAAArB,GACAe,OAAAC,SAAA,CACAC,IAAA,EACAC,SAAA,YAGA,KCtIwQ,I,UCQpQ7B,GAAY,OACd,EACAb,EACAO,GACA,EACA,KACA,WACA,MAIF,EAAeM,EAAiB,O,oECnBhC,IAAIb,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,SAAS,CAACA,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,kBAAkBgB,YAAY,CAAC,WAAa,uCAAuC,QAAU,MAAM,UAAU,IAAI,SAAW,WAAW,IAAM,IAAI,KAAO,IAAI,MAAQ,IAAI,OAAS,OAAOlB,EAAG,MAAM,CAACE,YAAY,uBAAuBJ,EAAI6C,GAAI,IAAI,SAASC,GAAG,OAAO5C,EAAG,MAAM,CAAC6C,IAAID,EAAE1C,YAAY,WAAW4C,MAAOhD,EAAIiD,oBAAqB,IAAG,GAAG/C,EAAG,MAAM,CAACE,YAAY,gBAAgBJ,EAAI6C,GAAI,GAAG,SAASC,GAAG,OAAO5C,EAAG,MAAM,CAAC6C,IAAID,EAAE1C,YAAY,eAAe,IAAG,GAAGF,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,4BAA4BF,EAAG,MAAM,CAACE,YAAY,4BAA4BF,EAAG,MAAM,CAACE,YAAY,8BAA8BF,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,KAAK,CAACE,YAAY,gBAAgB,CAACF,EAAG,OAAO,CAACE,YAAY,aAAac,MAAM,CAAC,YAAY,UAAU,CAAClB,EAAIa,GAAG,WAAWX,EAAG,OAAO,CAACE,YAAY,mBAAmB,CAACJ,EAAIa,GAAG,OAAOX,EAAG,OAAO,CAACE,YAAY,aAAac,MAAM,CAAC,YAAY,UAAU,CAAClB,EAAIa,GAAG,aAAaX,EAAG,MAAM,CAACE,YAAY,6BAA6B,CAACF,EAAG,IAAI,CAACE,YAAY,iCAAiC,CAACJ,EAAIa,GAAG,UAAUX,EAAG,IAAI,CAACE,YAAY,gCAAgCgB,YAAY,CAAC,kBAAkB,OAAO,CAACpB,EAAIa,GAAG,iBAAiBX,EAAG,IAAI,CAACE,YAAY,gCAAgCgB,YAAY,CAAC,kBAAkB,OAAO,CAACpB,EAAIa,GAAG,+BAA+BX,EAAG,UAAU,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,QAAQ,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAACF,EAAG,KAAK,CAACF,EAAIa,GAAG,UAAUX,EAAG,IAAI,CAACkB,YAAY,CAAC,YAAY,SAAS,CAACpB,EAAIa,GAAG,+GAAiHX,EAAG,IAAI,CAACkB,YAAY,CAAC,YAAY,SAAS,CAACpB,EAAIa,GAAG,qFAAqFX,EAAG,IAAI,CAACkB,YAAY,CAAC,YAAY,SAAS,CAACpB,EAAIa,GAAG,iFAAiFX,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,qBAAqB,CAACF,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,MAAM,CAACgB,MAAM,CAAC,IAAM,wBAAwB,IAAM,WAAW,QAAU,sBAAsBhB,EAAG,UAAU,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,KAAK,CAACE,YAAY,kBAAkB,CAACJ,EAAIa,GAAG,eAAeX,EAAG,MAAM,CAACE,YAAY,QAAQ,CAACF,EAAG,MAAM,CAACE,YAAY,kCAAkC,CAACF,EAAG,MAAM,CAACE,YAAY,qBAAqB,CAACF,EAAG,MAAM,CAACE,YAAY,2BAA2B,CAACF,EAAG,IAAI,CAACE,YAAY,iBAAiBgB,YAAY,CAAC,YAAY,OAAO,MAAQ,eAAelB,EAAG,KAAK,CAACE,YAAY,2BAA2B,CAACJ,EAAIa,GAAG,aAAaX,EAAG,MAAM,CAACE,YAAY,0BAA0B,CAACF,EAAG,IAAI,CAACF,EAAIa,GAAG,8CAA8CX,EAAG,MAAM,CAACE,YAAY,kCAAkC,CAACF,EAAG,MAAM,CAACE,YAAY,qBAAqB,CAACF,EAAG,MAAM,CAACE,YAAY,2BAA2B,CAACF,EAAG,IAAI,CAACE,YAAY,gBAAgBgB,YAAY,CAAC,YAAY,OAAO,MAAQ,eAAelB,EAAG,KAAK,CAACE,YAAY,2BAA2B,CAACJ,EAAIa,GAAG,eAAeX,EAAG,MAAM,CAACE,YAAY,0BAA0B,CAACF,EAAG,IAAI,CAACF,EAAIa,GAAG,oCAAoCX,EAAG,MAAM,CAACE,YAAY,kCAAkC,CAACF,EAAG,MAAM,CAACE,YAAY,qBAAqB,CAACF,EAAG,MAAM,CAACE,YAAY,2BAA2B,CAACF,EAAG,IAAI,CAACE,YAAY,eAAegB,YAAY,CAAC,YAAY,OAAO,MAAQ,eAAelB,EAAG,KAAK,CAACE,YAAY,2BAA2B,CAACJ,EAAIa,GAAG,iBAAiBX,EAAG,MAAM,CAACE,YAAY,0BAA0B,CAACF,EAAG,IAAI,CAACF,EAAIa,GAAG,gCAAgCX,EAAG,MAAM,CAACE,YAAY,kCAAkC,CAACF,EAAG,MAAM,CAACE,YAAY,qBAAqB,CAACF,EAAG,MAAM,CAACE,YAAY,2BAA2B,CAACF,EAAG,IAAI,CAACE,YAAY,gBAAgBgB,YAAY,CAAC,YAAY,OAAO,MAAQ,eAAelB,EAAG,KAAK,CAACE,YAAY,2BAA2B,CAACJ,EAAIa,GAAG,eAAeX,EAAG,MAAM,CAACE,YAAY,0BAA0B,CAACF,EAAG,IAAI,CAACF,EAAIa,GAAG,0CAA0CX,EAAG,UAAU,CAACE,YAAY,YAAY,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,KAAK,CAACE,YAAY,kBAAkB,CAACJ,EAAIa,GAAG,UAAUX,EAAG,IAAI,CAACE,YAAY,wBAAwB,CAACJ,EAAIa,GAAG,wCAAwCX,EAAG,MAAM,CAACE,YAAY,QAAQ,CAACF,EAAG,MAAM,CAACE,YAAY,wBAAwB,CAACF,EAAG,MAAM,CAACE,YAAY,YAAY,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACgB,MAAM,CAAC,IAAM,sBAAsB,IAAM,OAAO,QAAU,YAAYhB,EAAG,MAAM,CAACE,YAAY,qBAAqB,CAACF,EAAG,KAAK,CAACE,YAAY,qBAAqB,CAACJ,EAAIa,GAAG,UAAUX,EAAG,OAAO,CAACE,YAAY,yBAAyB,CAACJ,EAAIa,GAAG,+BAA+BX,EAAG,MAAM,CAACE,YAAY,wBAAwB,CAACF,EAAG,MAAM,CAACE,YAAY,YAAY,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACgB,MAAM,CAAC,IAAM,oBAAoB,IAAM,OAAO,QAAU,YAAYhB,EAAG,MAAM,CAACE,YAAY,qBAAqB,CAACF,EAAG,KAAK,CAACE,YAAY,qBAAqB,CAACJ,EAAIa,GAAG,UAAUX,EAAG,OAAO,CAACE,YAAY,yBAAyB,CAACJ,EAAIa,GAAG,8BAA8BX,EAAG,MAAM,CAACE,YAAY,wBAAwB,CAACF,EAAG,MAAM,CAACE,YAAY,YAAY,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACgB,MAAM,CAAC,IAAM,kBAAkB,IAAM,OAAO,QAAU,YAAYhB,EAAG,MAAM,CAACE,YAAY,qBAAqB,CAACF,EAAG,KAAK,CAACE,YAAY,qBAAqB,CAACJ,EAAIa,GAAG,UAAUX,EAAG,OAAO,CAACE,YAAY,yBAAyB,CAACJ,EAAIa,GAAG,kCAAkCX,EAAG,UAAU,CAACE,YAAY,mBAAmB,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,QAAQ,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,IAAI,CAACE,YAAY,kBAAkBF,EAAG,KAAK,CAACF,EAAIa,GAAG,UAAUX,EAAG,IAAI,CAACF,EAAIa,GAAG,qBAAqBX,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,IAAI,CAACE,YAAY,qBAAqBF,EAAG,KAAK,CAACF,EAAIa,GAAG,WAAWX,EAAG,IAAI,CAACF,EAAIa,GAAG,uBAAuBX,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,IAAI,CAACE,YAAY,uBAAuBF,EAAG,KAAK,CAACF,EAAIa,GAAG,UAAUX,EAAG,IAAI,CAACF,EAAIa,GAAG,sDAAsDX,EAAG,UAAU,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,KAAK,CAACF,EAAIa,GAAG,iCAAiCX,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,SAAS,CAACE,YAAY,wBAAwBU,GAAG,CAAC,MAAQd,EAAIkD,aAAa,CAAClD,EAAIa,GAAG,iBAC58M,EACIP,EAAkB,G,oBCyQtB,GACAC,KAAA,YACAC,WAAA,CAAA2C,OAAAA,EAAAA,GACA9B,OACA,OACA+B,gBAAA,GAEA,EACAC,UACA,KAAAC,0BACA,KAAAC,wBACA,KAAAC,oBACA,KAAAC,0BACA,KAAAC,wBACA,EACApC,QAAA,CACA4B,aACA,KAAAR,QAAAE,KAAA,WACA,EACAe,YAEAC,QAAAC,IAAA,OACA,EACAZ,mBACA,OACAa,KAAA,IAAAC,KAAAC,SAAA,IACAxB,IAAA,IAAAuB,KAAAC,SAAA,IACAC,eAAA,GAAAF,KAAAC,SAAA,IACAE,kBAAA,GAAAH,KAAAC,SAAA,OAEA,EACAG,eACA,OACAL,KAAA,GAAAC,KAAAC,SAAA,MACAxB,IAAA,GAAAuB,KAAAC,SAAA,MACAC,eAAA,EAAAF,KAAAC,SAAA,IAEA,EACAV,0BACA,MAAAc,EAAA,GACA,QAAAC,EAAA,EAAAA,EAAA,GAAAA,IACAD,EAAAxB,KAAA,CACA0B,GAAAD,EACAE,GAAA,IAAAR,KAAAC,SACAQ,GAAA,IAAAT,KAAAC,SACAS,GAAA,IAAAV,KAAAC,SACAU,GAAA,IAAAX,KAAAC,WAGA,KAAAZ,gBAAAgB,CACA,EACAb,wBACA,MAAAoB,EAAA/C,SAAAC,iBAAA,gBACA8C,EAAA7C,SAAA8C,IACA,MAAAC,EAAAC,SAAAF,EAAAG,aAAA,gBACAC,EAAAH,EAAA,IACA,IAAAI,EAAA,EAEA,MAAAC,EAAAC,aAAA,KACAF,GAAAD,EACAC,GAAAJ,GACAD,EAAAQ,YAAAP,EACAQ,cAAAH,IAEAN,EAAAQ,YAAArB,KAAAuB,MAAAL,EACA,GACA,MAEA,EAGAzB,oBAEA,MAAA+B,EAAAjD,OAAAkD,YAAA,IAEAD,IAEA,KAAAE,yBAGA,KAAAC,sBAGA,KAAAC,wBAEA,EAEAF,yBAEA,MAAAG,EAAAhE,SAAAC,iBAAA,aACA+D,EAAA9D,SAAA,CAAA+D,EAAAC,KACAA,EAAA,KACAD,EAAA7C,MAAA+C,QAAA,OACA,IAIA,MAAAC,EAAApE,SAAAC,iBAAA,gBACAmE,EAAAlE,SAAA,CAAAmE,EAAAH,KACAA,EAAA,IACAG,EAAAjD,MAAA+C,QAAA,OACA,GAEA,EAEAL,sBAEA,MAAAQ,EAAAtE,SAAAC,iBAAA,yDAEAqE,EAAApE,SAAAqE,IACAA,EAAAC,iBAAA,mBACAD,EAAAnD,MAAAqD,UAAA,qBACA,CAAAC,SAAA,IAEAH,EAAAC,iBAAA,iBACAjE,YAAA,KACAgE,EAAAnD,MAAAqD,UAAA,KACA,OACA,CAAAC,SAAA,MAEA,EAEAX,wBACA,MAAAY,EAAA,CACA,4BACA,sBAGAA,EAAAzE,SAAA0E,IACA,MAAAC,EAAA,IAAAC,MACAD,EAAAD,IAAAA,CAAA,GAEA,EAEA/C,0BAEAnB,OAAA8D,iBAAA,0BACAjE,YAAA,KAEA,KAAAmB,0BAGA,KAAAE,oBAGA,KAAAE,wBAAA,GACA,OAEA,EAGAA,yBACA,MAAAiD,EAAArE,OAAAkD,YAAA,KAEAmB,GAEA/E,SAAAgF,KAAA5E,UAAAE,IAAA,qBAGA,KAAA2E,wBAGA,KAAAC,mCAEAlF,SAAAgF,KAAA5E,UAAAI,OAAA,oBAEA,EAEAyE,wBACA,MAAAE,EAAAnF,SAAAoF,cAAA,oBACAD,GAAAzE,OAAAkD,YAAA,OAEAuB,EAAA/D,MAAAiE,eAAA,UACAF,EAAA/D,MAAAkE,iBAAA,YACAH,EAAA/D,MAAAmE,mBAAA,SAEA,EAEAL,kCAEA,GAAAxE,OAAAkD,YAAA,MAEA,MAAA4B,EAAAxF,SAAAoF,cAAA,wBACA,GAAAI,GAAAA,EAAAC,SAAAC,OAAA,GACA,QAAAjD,EAAA,GAAAA,EAAA,GAAAA,IAAA,CACA,MAAAwB,EAAAjE,SAAA2F,cAAA,OACA1B,EAAA2B,UAAA,WACA3B,EAAA7C,MAAAyE,QAAA,yBACA,IAAA1D,KAAAC,kCACA,IAAAD,KAAAC,8CACA,GAAAD,KAAAC,iDACA,GAAAD,KAAAC,SAAA,qBAEAoD,EAAAM,YAAA7B,EACA,CAEA,CACA,GAGA8B,gBAEA/F,SAAAgF,KAAA5E,UAAAI,OAAA,oBACA,GCtdkQ,I,UCQ9PxB,GAAY,OACd,EACAb,EACAO,GACA,EACA,KACA,WACA,MAIF,EAAeM,EAAiB,O", "sources": ["webpack://portal-ui/./src/components/common/Layout.vue", "webpack://portal-ui/src/components/common/Layout.vue", "webpack://portal-ui/./src/components/common/Layout.vue?a648", "webpack://portal-ui/./src/components/common/Layout.vue?e255", "webpack://portal-ui/./src/components/common/footer/Footer.vue", "webpack://portal-ui/src/components/common/footer/Footer.vue", "webpack://portal-ui/./src/components/common/footer/Footer.vue?6062", "webpack://portal-ui/./src/components/common/footer/Footer.vue?8b5d", "webpack://portal-ui/./src/views/About/AboutView.vue", "webpack://portal-ui/src/views/About/AboutView.vue", "webpack://portal-ui/./src/views/About/AboutView.vue?f941", "webpack://portal-ui/./src/views/About/AboutView.vue?7bed"], "sourcesContent": ["var render = function render(){var _vm=this,_c=_vm._self._c;return _c('main',{staticClass:\"page-wrapper\"},[_vm._t(\"default\"),_c('Mider'),_c('chatAi'),_c('Footer')],2)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n\t<main class=\"page-wrapper\">\r\n\t\t<!-- <Header/> -->\r\n\t\t\t<slot></slot>\r\n    <Mider/>\r\n    <chatAi/>\r\n\t\t<Footer/>\r\n\t</main>\r\n</template>\r\n\r\n<script>\r\n// import Header from \"@/components/common/header/Header\";\r\nimport Footer from \"@/components/common/footer/Footer\";\r\nimport Mider from \"@/components/common/mider/Mider\";\r\nimport chatAi from \"@/components/common/mider/chatAi\";\r\n\r\nexport default {\r\n\tname: \"Layout\",\r\n\tcomponents:{Footer, Mider, chatAi}\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.main-content{\r\n\twidth: 100%;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n}\r\n</style>\r\n", "import mod from \"-!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./Layout.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./Layout.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./Layout.vue?vue&type=template&id=0ce69a34&scoped=true&\"\nimport script from \"./Layout.vue?vue&type=script&lang=js&\"\nexport * from \"./Layout.vue?vue&type=script&lang=js&\"\nimport style0 from \"./Layout.vue?vue&type=style&index=0&id=0ce69a34&prod&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"0ce69a34\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"footer\"},[_c('div',{staticClass:\"footer-content\"},[_c('div',{staticClass:\"footer-column\"},[_c('div',{staticClass:\"footer-title\"},[_vm._v(\"售前咨询热线\")]),_c('div',{staticClass:\"footer-phone\"},[_vm._v(\"13913283376\")]),_c('div',{staticClass:\"footer-links\"},[_c('a',{on:{\"click\":function($event){return _vm.navigateTo('/index')}}},[_c('div',{staticClass:\"footer-link\"},[_vm._v(\"首页\")])]),_c('a',{on:{\"click\":function($event){return _vm.navigateTo('/product')}}},[_c('div',{staticClass:\"footer-link\"},[_vm._v(\"AI算力市场\")])])])]),_c('div',{staticClass:\"footer-column\"},[_c('div',{staticClass:\"footer-title\"},[_vm._v(\"支持与服务\")]),_c('div',{staticClass:\"footer-links\"},[_c('div',{staticClass:\"footer-link\"},[_vm._v(\"联系我们\")]),_c('a',{on:{\"click\":function($event){return _vm.navigateTo('/help')}}},[_c('div',{staticClass:\"footer-link\"},[_vm._v(\"帮助文档\")])]),_c('div',{staticClass:\"footer-link\"},[_vm._v(\"公告\")]),_c('div',{staticClass:\"footer-link\"},[_vm._v(\"提交建议\")])])]),_vm._m(0),_vm._m(1),_vm._m(2)]),_vm._m(3)])\n}\nvar staticRenderFns = [function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"footer-column\"},[_c('div',{staticClass:\"footer-title\"},[_vm._v(\"关注天工开物\")]),_c('div',{staticClass:\"footer-links\"},[_c('div',{staticClass:\"footer-link\"},[_vm._v(\"关注天工开物\")]),_c('div',{staticClass:\"footer-link\"},[_vm._v(\"天工开物公众号\")]),_c('div',{staticClass:\"footer-link\"},[_vm._v(\"天工开物微博\")]),_c('div',{staticClass:\"footer-link\"},[_vm._v(\"天工开物支持与服务\")])])])\n},function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"footer-column\"},[_c('div',{staticClass:\"footer-title\"},[_vm._v(\"联系专属客服\")]),_c('div',{staticClass:\"footer-qrcode\"},[_c('img',{attrs:{\"src\":require(\"@/assets/images/footer/wechat.jpg\"),\"alt\":\"联系专属客服二维码\"}})])])\n},function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"footer-column\"},[_c('div',{staticClass:\"footer-title\"},[_vm._v(\"官方公众号\")]),_c('div',{staticClass:\"footer-qrcode\"},[_c('img',{attrs:{\"src\":require(\"@/assets/images/footer/wechat.jpg\"),\"alt\":\"官方公众号二维码\"}})])])\n},function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"footer-bottom\"},[_c('div',{staticClass:\"footer-copyright\"},[_c('a',{staticStyle:{\"color\":\"inherit\",\"text-decoration\":\"none\"},attrs:{\"href\":\"https://beian.miit.gov.cn\",\"target\":\"_blank\"}},[_vm._v(\" 苏ICP备2025171841号-1 \")]),_vm._v(\" 天工开物智能科技（苏州）有限公司 ALL RIGHTS RESERVED. \")])])\n}]\n\nexport { render, staticRenderFns }", "<template>\r\n  <div class=\"footer\">\r\n    <div class=\"footer-content\">\r\n      <!-- First column -->\r\n      <div class=\"footer-column\">\r\n        <div class=\"footer-title\">售前咨询热线</div>\r\n        <div class=\"footer-phone\">13913283376</div>\r\n        <div class=\"footer-links\">\r\n          <a @click=\"navigateTo('/index')\"><div class=\"footer-link\">首页</div></a>\r\n          <a @click=\"navigateTo('/product')\"><div class=\"footer-link\">AI算力市场</div></a>\r\n\r\n<!--          <div class=\"footer-link\">AI算力市场</div>-->\r\n\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Second column -->\r\n      <div class=\"footer-column\">\r\n        <div class=\"footer-title\">支持与服务</div>\r\n        <div class=\"footer-links\">\r\n          <div class=\"footer-link\">联系我们</div>\r\n          <a @click=\"navigateTo('/help')\"><div class=\"footer-link\">帮助文档</div></a>\r\n          <div class=\"footer-link\">公告</div>\r\n          <div class=\"footer-link\">提交建议</div>\r\n        </div>\r\n      </div>\r\n\r\n<!--      &lt;!&ndash; Third column &ndash;&gt;-->\r\n<!--      <div class=\"footer-column\">-->\r\n<!--        <div class=\"footer-title\">账户管理</div>-->\r\n<!--        <div class=\"footer-links\">-->\r\n<!--          <div class=\"footer-link\">控制台</div>-->\r\n<!--          <div class=\"footer-link\">账号管理</div>-->\r\n<!--          <div class=\"footer-link\">充值付款</div>-->\r\n<!--          <div class=\"footer-link\">线下款 / 电汇</div>-->\r\n<!--          <div class=\"footer-link\">索取发票</div>-->\r\n<!--          <div class=\"footer-link\">合规性</div>-->\r\n<!--        </div>-->\r\n<!--      </div>-->\r\n\r\n      <!-- Fourth column -->\r\n      <div class=\"footer-column\">\r\n        <div class=\"footer-title\">关注天工开物</div>\r\n        <div class=\"footer-links\">\r\n          <div class=\"footer-link\">关注天工开物</div>\r\n          <div class=\"footer-link\">天工开物公众号</div>\r\n          <div class=\"footer-link\">天工开物微博</div>\r\n          <div class=\"footer-link\">天工开物支持与服务</div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Fifth column -->\r\n      <div class=\"footer-column\">\r\n        <div class=\"footer-title\">联系专属客服</div>\r\n        <div class=\"footer-qrcode\">\r\n          <img src=\"@/assets/images/footer/wechat.jpg\" alt=\"联系专属客服二维码\" />\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Sixth column -->\r\n      <div class=\"footer-column\">\r\n        <div class=\"footer-title\">官方公众号</div>\r\n        <div class=\"footer-qrcode\">\r\n          <img src=\"@/assets/images/footer/wechat.jpg\" alt=\"官方公众号二维码\" />\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Bottom footer links -->\r\n    <div class=\"footer-bottom\">\r\n\r\n      <div class=\"footer-copyright\">\r\n        <a href=\"https://beian.miit.gov.cn\" target=\"_blank\" style=\"color: inherit; text-decoration: none;\">\r\n          苏ICP备2025171841号-1\r\n        </a>\r\n           天工开物智能科技（苏州）有限公司 ALL RIGHTS RESERVED. </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"Footer\",\r\n  data() {\r\n    return {\r\n      // Data can be added here if needed\r\n    }\r\n  },\r\n  methods: {\r\n    navigateTo(path) {\r\n      // 记录当前活动路径作为上一个活动路径\r\n      if (this.currentPath && this.currentPath !== path) {\r\n        this.previousActivePath = this.currentPath;\r\n\r\n        // 为当前活动链接和登录按钮添加 active-exit 类\r\n        this.$nextTick(() => {\r\n          const navLinks = document.querySelectorAll('.nav-link, .btn-login');\r\n          navLinks.forEach(link => {\r\n            if ((link.classList.contains('active') ||\r\n                    (path === '/login' && link.classList.contains('btn-login'))) &&\r\n                !link.classList.contains('active-exit')) {\r\n              link.classList.add('active-exit');\r\n\r\n              // 等待动画完成后移除 active-exit 类\r\n              setTimeout(() => {\r\n                link.classList.remove('active-exit');\r\n              }, 300); // 匹配你的 CSS transition 持续时间 (0.3s)\r\n            }\r\n          });\r\n\r\n          // 更新当前路径\r\n          this.currentPath = path;\r\n        });\r\n      } else {\r\n        this.currentPath = path;\r\n      }\r\n\r\n      // 如果当前路径与目标路径相同，则重新加载页面\r\n      if (this.$route.path === path) {\r\n        this.$nextTick(() => {\r\n          window.scrollTo({\r\n            top: 0,\r\n            behavior: 'instant' // 使用即时滚动而不是平滑滚动\r\n          });\r\n          this.$router.go(0); // 刷新当前页面\r\n        });\r\n      } else {\r\n        // 不同路径，正常导航并滚动到顶部\r\n        this.$router.push(path);\r\n        window.scrollTo({\r\n          top: 0,\r\n          behavior: 'instant'\r\n        });\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.footer {\r\n  max-width: 2560px;\r\n  width: 100%;\r\n  background-color: #424242;\r\n  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;\r\n  color: white;\r\n  padding: 0;\r\n  margin: 0px;\r\n}\r\n\r\n.footer-content {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  max-width: 2560px;\r\n  margin: 0 40px;\r\n  padding: 20px 0;\r\n}\r\n\r\n.footer-column {\r\n  flex: 1;\r\n  min-width: 150px;\r\n  padding: 0 15px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.footer-title {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  margin-bottom: 15px;\r\n  color: white;\r\n}\r\n\r\n.footer-phone {\r\n  font-size: 16px;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.footer-links {\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.footer-link {\r\n  margin-bottom: 10px;\r\n  cursor: pointer;\r\n  color: white;\r\n  font-size: 14px;\r\n  transition: color 0.2s;\r\n}\r\n\r\n.footer-link:hover {\r\n  color: #1890ff;\r\n}\r\n\r\n.footer-qrcode {\r\n  width: 120px;\r\n  height: 120px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.footer-qrcode img {\r\n  max-width: 100%;\r\n  max-height: 100%;\r\n  margin-left: -18%;\r\n}\r\n\r\n.footer-bottom {\r\n  border-top: 1px solid #e8e8e8;\r\n  padding: 20px 0;\r\n  text-align: center;\r\n  max-width: 2560px;\r\n  margin: 0 40px;\r\n}\r\n\r\n.footer-bottom-links {\r\n  margin-bottom: 10px;\r\n  text-align: left;\r\n}\r\n\r\n.footer-bottom-link {\r\n  color: white;\r\n  margin: 0 10px;\r\n  font-size: 14px;\r\n  text-decoration: none;\r\n  transition: color 0.2s;\r\n}\r\n\r\n.footer-bottom-link:hover {\r\n  color: #1890ff;\r\n  text-decoration: underline;\r\n}\r\n\r\n.footer-copyright, .footer-license {\r\n  font-size: 15px;\r\n  text-align: center;\r\n  color: white;\r\n  margin-left: 10px;\r\n}\r\n</style>", "import mod from \"-!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./Footer.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./Footer.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./Footer.vue?vue&type=template&id=2d6e9349&scoped=true&\"\nimport script from \"./Footer.vue?vue&type=script&lang=js&\"\nexport * from \"./Footer.vue?vue&type=script&lang=js&\"\nimport style0 from \"./Footer.vue?vue&type=style&index=0&id=2d6e9349&prod&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"2d6e9349\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('Layout',[_c('div',{staticClass:\"about-banner\"},[_c('div',{staticClass:\"about-banner-bg\",staticStyle:{\"background\":\"url('images/earth.gif') center/cover\",\"opacity\":\"0.7\",\"z-index\":\"1\",\"position\":\"absolute\",\"top\":\"0\",\"left\":\"0\",\"right\":\"0\",\"bottom\":\"0\"}}),_c('div',{staticClass:\"particles-container\"},_vm._l((50),function(n){return _c('div',{key:n,staticClass:\"particle\",style:(_vm.getParticleStyle())})}),0),_c('div',{staticClass:\"data-streams\"},_vm._l((8),function(n){return _c('div',{key:n,staticClass:\"data-stream\"})}),0),_c('div',{staticClass:\"light-effects\"},[_c('div',{staticClass:\"light-beam light-beam-1\"}),_c('div',{staticClass:\"light-beam light-beam-2\"}),_c('div',{staticClass:\"light-beam light-beam-3\"})]),_c('div',{staticClass:\"banner-content\"},[_c('h1',{staticClass:\"banner-title\"},[_c('span',{staticClass:\"title-word\",attrs:{\"data-text\":\"承天工之智\"}},[_vm._v(\"承天工之智\")]),_c('span',{staticClass:\"title-separator\"},[_vm._v(\"，\")]),_c('span',{staticClass:\"title-word\",attrs:{\"data-text\":\"启万物之能\"}},[_vm._v(\"启万物之能\")])]),_c('div',{staticClass:\"banner-subtitle-container\"},[_c('p',{staticClass:\"banner-subtitle typing-effect\"},[_vm._v(\"我们相信\")]),_c('p',{staticClass:\"banner-subtitle typing-effect\",staticStyle:{\"animation-delay\":\"1s\"}},[_vm._v(\"人类无需再围成一台机器\")]),_c('p',{staticClass:\"banner-subtitle typing-effect\",staticStyle:{\"animation-delay\":\"2s\"}},[_vm._v(\"而是用智能连接彼此，释放算力的真正价值\")])])])]),_c('section',{staticClass:\"about-section\"},[_c('div',{staticClass:\"container\"},[_c('div',{staticClass:\"am-g\"},[_c('div',{staticClass:\"am-u-md-6\"},[_c('div',{staticClass:\"our-company-text\"},[_c('h1',[_vm._v(\"关于我们\")]),_c('p',{staticStyle:{\"font-size\":\"17px\"}},[_vm._v(\" 天工开物智能科技（苏州）有限公司，致力于打造面向企业级用户的高性能计算解决方案， 围绕\\\"高效调度、低门槛使用、专业保障\\\"的核心理念，为 AI、大模型、图形渲染、科研计算等场景提供灵活、稳定、弹性的算力支持。 \")]),_c('p',{staticStyle:{\"font-size\":\"17px\"}},[_vm._v(\" 我们基于全国分布式算力网络，自主构建智算调度平台，整合GPU资源与数据中心节点， 为企业提供从算力资源租用、模型部署优化到全流程运维服务的一站式专业方案。 \")]),_c('p',{staticStyle:{\"font-size\":\"17px\"}},[_vm._v(\" 在智能时代的浪潮中，天工开物致力于打造企业级专业算力服务平台， 以全国分布式高性能计算网络为基础，提供稳定、高效、灵活可控的算力解决方案。 \")])])]),_c('div',{staticClass:\"am-u-md-6\"},[_c('div',{staticClass:\"our-company-quote\"},[_c('div',{staticClass:\"our-company-img\"},[_c('img',{attrs:{\"src\":\"images/tgkw_about.jpg\",\"alt\":\"天工开物智能科技\",\"loading\":\"lazy\"}})])])])])])]),_c('section',{staticClass:\"our-mission\"},[_c('div',{staticClass:\"container\"},[_c('div',{staticClass:\"section--header\"},[_c('h2',{staticClass:\"section--title\"},[_vm._v(\"选择我们的理由\")])]),_c('div',{staticClass:\"am-g\"},[_c('div',{staticClass:\"am-u-sm-12 am-u-md-6 am-u-lg-3\"},[_c('div',{staticClass:\"our_mission--item\"},[_c('div',{staticClass:\"our_mission--item_media\"},[_c('i',{staticClass:\"am-icon-server\",staticStyle:{\"font-size\":\"48px\",\"color\":\"#1470FF\"}})]),_c('h4',{staticClass:\"our_mission--item_title\"},[_vm._v(\"企业级专业服务\")]),_c('div',{staticClass:\"our_mission--item_body\"},[_c('p',[_vm._v(\"为AI、科研、图形渲染、工业仿真等场景，提供稳定高效的高性能计算支持\")])])])]),_c('div',{staticClass:\"am-u-sm-12 am-u-md-6 am-u-lg-3\"},[_c('div',{staticClass:\"our_mission--item\"},[_c('div',{staticClass:\"our_mission--item_media\"},[_c('i',{staticClass:\"am-icon-globe\",staticStyle:{\"font-size\":\"48px\",\"color\":\"#1470FF\"}})]),_c('h4',{staticClass:\"our_mission--item_title\"},[_vm._v(\"全国分布式节点布局\")]),_c('div',{staticClass:\"our_mission--item_body\"},[_c('p',[_vm._v(\"多地部署，动态调度，资源灵活，负载均衡，响应迅速\")])])])]),_c('div',{staticClass:\"am-u-sm-12 am-u-md-6 am-u-lg-3\"},[_c('div',{staticClass:\"our_mission--item\"},[_c('div',{staticClass:\"our_mission--item_media\"},[_c('i',{staticClass:\"am-icon-cogs\",staticStyle:{\"font-size\":\"48px\",\"color\":\"#1470FF\"}})]),_c('h4',{staticClass:\"our_mission--item_title\"},[_vm._v(\"灵活弹性 + 高性价比\")]),_c('div',{staticClass:\"our_mission--item_body\"},[_c('p',[_vm._v(\"自研调度平台，支持定制，与大客户深度合作\")])])])]),_c('div',{staticClass:\"am-u-sm-12 am-u-md-6 am-u-lg-3\"},[_c('div',{staticClass:\"our_mission--item\"},[_c('div',{staticClass:\"our_mission--item_media\"},[_c('i',{staticClass:\"am-icon-users\",staticStyle:{\"font-size\":\"48px\",\"color\":\"#1470FF\"}})]),_c('h4',{staticClass:\"our_mission--item_title\"},[_vm._v(\"更懂企业的算力伙伴\")]),_c('div',{staticClass:\"our_mission--item_body\"},[_c('p',[_vm._v(\"从需求对接、技术支持到运维保障，全流程一对一服务\")])])])])])])]),_c('section',{staticClass:\"our-team\"},[_c('div',{staticClass:\"container\"},[_c('div',{staticClass:\"section--header\"},[_c('h2',{staticClass:\"section--title\"},[_vm._v(\"核心团队\")]),_c('p',{staticClass:\"section--description\"},[_vm._v(\" 核心团队来自知名AI云计算厂商、IDC运维专家与高校科研机构 \")])]),_c('div',{staticClass:\"am-g\"},[_c('div',{staticClass:\"am-u-sm-12 am-u-md-4\"},[_c('div',{staticClass:\"team-box\"},[_c('div',{staticClass:\"our-team-img\"},[_c('img',{attrs:{\"src\":\"images/techteam.png\",\"alt\":\"技术团队\",\"loading\":\"lazy\"}})]),_c('div',{staticClass:\"team_member--body\"},[_c('h4',{staticClass:\"team_member--name\"},[_vm._v(\"技术研发\")]),_c('span',{staticClass:\"team_member--position\"},[_vm._v(\"专业的研发团队，深耕AI算力调度与优化\")])])])]),_c('div',{staticClass:\"am-u-sm-12 am-u-md-4\"},[_c('div',{staticClass:\"team-box\"},[_c('div',{staticClass:\"our-team-img\"},[_c('img',{attrs:{\"src\":\"images/yunwei.png\",\"alt\":\"运维团队\",\"loading\":\"lazy\"}})]),_c('div',{staticClass:\"team_member--body\"},[_c('h4',{staticClass:\"team_member--name\"},[_vm._v(\"运维保障\")]),_c('span',{staticClass:\"team_member--position\"},[_vm._v(\"5x8小时专业运维，确保服务稳定可靠\")])])])]),_c('div',{staticClass:\"am-u-sm-12 am-u-md-4\"},[_c('div',{staticClass:\"team-box\"},[_c('div',{staticClass:\"our-team-img\"},[_c('img',{attrs:{\"src\":\"images/khjl.png\",\"alt\":\"客服团队\",\"loading\":\"lazy\"}})]),_c('div',{staticClass:\"team_member--body\"},[_c('h4',{staticClass:\"team_member--name\"},[_vm._v(\"客户服务\")]),_c('span',{staticClass:\"team_member--position\"},[_vm._v(\"专业客户经理，提供一对一贴心服务\")])])])])])])]),_c('section',{staticClass:\"contact-section\"},[_c('div',{staticClass:\"container\"},[_c('div',{staticClass:\"am-g\"},[_c('div',{staticClass:\"am-u-md-4\"},[_c('div',{staticClass:\"contact-item\"},[_c('i',{staticClass:\"am-icon-phone\"}),_c('h4',[_vm._v(\"联系电话\")]),_c('p',[_vm._v(\"13913283376\")])])]),_c('div',{staticClass:\"am-u-md-4\"},[_c('div',{staticClass:\"contact-item\"},[_c('i',{staticClass:\"am-icon-envelope\"}),_c('h4',[_vm._v(\"官方公众号\")]),_c('p',[_vm._v(\"昆山新质创新数字技术研究院\")])])]),_c('div',{staticClass:\"am-u-md-4\"},[_c('div',{staticClass:\"contact-item\"},[_c('i',{staticClass:\"am-icon-map-marker\"}),_c('h4',[_vm._v(\"公司地址\")]),_c('p',[_vm._v(\"江苏省苏州市昆山市玉山镇祖冲之路1699号昆山工业技术研究院综合南楼1404\")])])])])])]),_c('section',{staticClass:\"cta-section\"},[_c('div',{staticClass:\"cta-content\"},[_c('h2',[_vm._v(\"连接智算未来，让高性能计算像水电一样可得、可控、可负担\")]),_c('div',{staticClass:\"cta-buttons\"},[_c('button',{staticClass:\"am-btn am-btn-primary\",on:{\"click\":_vm.startTrial}},[_vm._v(\"立即开始\")])])])])])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <Layout>\r\n    <!-- 顶部Banner -->\r\n    <div class=\"about-banner\">\r\n      <!-- 背景图层 -->\r\n      <div\r\n        class=\"about-banner-bg\"\r\n        style=\"background: url('images/earth.gif') center/cover; opacity: 0.7; z-index: 1; position: absolute; top: 0; left: 0; right: 0; bottom: 0;\"\r\n      ></div>\r\n\r\n      <!-- 动态网格背景 -->\r\n      <!-- <div class=\"grid-background\"></div> -->\r\n\r\n      <!-- 粒子效果 -->\r\n      <div class=\"particles-container\">\r\n        <div class=\"particle\" v-for=\"n in 50\" :key=\"n\" :style=\"getParticleStyle()\"></div>\r\n      </div>\r\n\r\n      <!-- 数据流效果 -->\r\n      <div class=\"data-streams\">\r\n        <div class=\"data-stream\" v-for=\"n in 8\" :key=\"n\"></div>\r\n      </div>\r\n\r\n      <!-- 算力节点连接线 -->\r\n      <!-- <div class=\"network-nodes\">\r\n        <div class=\"node\" v-for=\"n in 12\" :key=\"n\" :style=\"getNodeStyle()\">\r\n          <div class=\"node-pulse\"></div>\r\n        </div>\r\n        <svg class=\"connection-lines\" viewBox=\"0 0 100 100\">\r\n          <line v-for=\"line in connectionLines\" :key=\"line.id\"\r\n                :x1=\"line.x1\" :y1=\"line.y1\" :x2=\"line.x2\" :y2=\"line.y2\"\r\n                class=\"connection-line\"></line>\r\n        </svg>\r\n      </div> -->\r\n\r\n      <!-- 光效层 -->\r\n      <div class=\"light-effects\">\r\n        <div class=\"light-beam light-beam-1\"></div>\r\n        <div class=\"light-beam light-beam-2\"></div>\r\n        <div class=\"light-beam light-beam-3\"></div>\r\n      </div>\r\n\r\n      <div class=\"banner-content\">\r\n        <h1 class=\"banner-title\">\r\n          <span class=\"title-word\" data-text=\"承天工之智\">承天工之智</span>\r\n          <span class=\"title-separator\">，</span>\r\n          <span class=\"title-word\" data-text=\"启万物之能\">启万物之能</span>\r\n        </h1>\r\n        <div class=\"banner-subtitle-container\">\r\n          <p class=\"banner-subtitle typing-effect\">我们相信</p>\r\n          <p class=\"banner-subtitle typing-effect\" style=\"animation-delay: 1s;\">人类无需再围成一台机器</p>\r\n          <p class=\"banner-subtitle typing-effect\" style=\"animation-delay: 2s;\">而是用智能连接彼此，释放算力的真正价值</p>\r\n        </div>\r\n\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 关于我们 -->\r\n    <section class=\"about-section\">\r\n      <div class=\"container\">\r\n        <div class=\"am-g\">\r\n          <div class=\"am-u-md-6\">\r\n            <div class=\"our-company-text\">\r\n              <h1>关于我们</h1>\r\n              <p style=\"font-size: 17px;\">\r\n                天工开物智能科技（苏州）有限公司，致力于打造面向企业级用户的高性能计算解决方案，\r\n                围绕\"高效调度、低门槛使用、专业保障\"的核心理念，为 AI、大模型、图形渲染、科研计算等场景提供灵活、稳定、弹性的算力支持。\r\n              </p>\r\n              <p style=\"font-size: 17px;\">\r\n                我们基于全国分布式算力网络，自主构建智算调度平台，整合GPU资源与数据中心节点，\r\n                为企业提供从算力资源租用、模型部署优化到全流程运维服务的一站式专业方案。\r\n              </p>\r\n              <p style=\"font-size: 17px;\">\r\n                在智能时代的浪潮中，天工开物致力于打造企业级专业算力服务平台，\r\n                以全国分布式高性能计算网络为基础，提供稳定、高效、灵活可控的算力解决方案。\r\n              </p>\r\n            </div>\r\n          </div>\r\n          <div class=\"am-u-md-6\">\r\n            <div class=\"our-company-quote\">\r\n              <div class=\"our-company-img\">\r\n                <img src=\"images/tgkw_about.jpg\" alt=\"天工开物智能科技\" loading=\"lazy\">\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n\r\n    <!-- 数据统计 -->\r\n    <!-- <section class=\"stats-section\">\r\n      <div class=\"container\">\r\n        <div class=\"am-g\">\r\n          <div class=\"am-u-sm-6 am-u-md-3\">\r\n            <div class=\"stat-item\">\r\n              <div class=\"stat-number\">32%</div>\r\n              <div class=\"stat-label\">市场占有率</div>\r\n            </div>\r\n          </div>\r\n          <div class=\"am-u-sm-6 am-u-md-3\">\r\n            <div class=\"stat-item\">\r\n              <div class=\"stat-number\">27%</div>\r\n              <div class=\"stat-label\">年增长率</div>\r\n            </div>\r\n          </div>\r\n          <div class=\"am-u-sm-6 am-u-md-3\">\r\n            <div class=\"stat-item\">\r\n              <div class=\"stat-number\">3000+</div>\r\n              <div class=\"stat-label\">服务客户</div>\r\n            </div>\r\n          </div>\r\n          <div class=\"am-u-sm-6 am-u-md-3\">\r\n            <div class=\"stat-item\">\r\n              <div class=\"stat-number\">10000+</div>\r\n              <div class=\"stat-label\">GPU节点</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section> -->\r\n\r\n    <!-- 选择我们的理由 -->\r\n    <section class=\"our-mission\">\r\n      <div class=\"container\">\r\n        <div class=\"section--header\">\r\n          <h2 class=\"section--title\">选择我们的理由</h2>\r\n        </div>\r\n        <div class=\"am-g\">\r\n          <div class=\"am-u-sm-12 am-u-md-6 am-u-lg-3\">\r\n            <div class=\"our_mission--item\">\r\n              <div class=\"our_mission--item_media\">\r\n                <i class=\"am-icon-server\" style=\"font-size: 48px; color: #1470FF;\"></i>\r\n              </div>\r\n              <h4 class=\"our_mission--item_title\">企业级专业服务</h4>\r\n              <div class=\"our_mission--item_body\">\r\n                <p>为AI、科研、图形渲染、工业仿真等场景，提供稳定高效的高性能计算支持</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"am-u-sm-12 am-u-md-6 am-u-lg-3\">\r\n            <div class=\"our_mission--item\">\r\n              <div class=\"our_mission--item_media\">\r\n                <i class=\"am-icon-globe\" style=\"font-size: 48px; color: #1470FF;\"></i>\r\n              </div>\r\n              <h4 class=\"our_mission--item_title\">全国分布式节点布局</h4>\r\n              <div class=\"our_mission--item_body\">\r\n                <p>多地部署，动态调度，资源灵活，负载均衡，响应迅速</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"am-u-sm-12 am-u-md-6 am-u-lg-3\">\r\n            <div class=\"our_mission--item\">\r\n              <div class=\"our_mission--item_media\">\r\n                <i class=\"am-icon-cogs\" style=\"font-size: 48px; color: #1470FF;\"></i>\r\n              </div>\r\n              <h4 class=\"our_mission--item_title\">灵活弹性 + 高性价比</h4>\r\n              <div class=\"our_mission--item_body\">\r\n                <p>自研调度平台，支持定制，与大客户深度合作</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"am-u-sm-12 am-u-md-6 am-u-lg-3\">\r\n            <div class=\"our_mission--item\">\r\n              <div class=\"our_mission--item_media\">\r\n                <i class=\"am-icon-users\" style=\"font-size: 48px; color: #1470FF;\"></i>\r\n              </div>\r\n              <h4 class=\"our_mission--item_title\">更懂企业的算力伙伴</h4>\r\n              <div class=\"our_mission--item_body\">\r\n                <p>从需求对接、技术支持到运维保障，全流程一对一服务</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n\r\n    <!-- 核心团队 -->\r\n    <section class=\"our-team\">\r\n      <div class=\"container\">\r\n        <div class=\"section--header\">\r\n          <h2 class=\"section--title\">核心团队</h2>\r\n          <p class=\"section--description\">\r\n            核心团队来自知名AI云计算厂商、IDC运维专家与高校科研机构\r\n          </p>\r\n        </div>\r\n        <div class=\"am-g\">\r\n          <div class=\"am-u-sm-12 am-u-md-4\">\r\n            <div class=\"team-box\">\r\n              <div class=\"our-team-img\">\r\n                <img src=\"images/techteam.png\" alt=\"技术团队\" loading=\"lazy\">\r\n              </div>\r\n              <div class=\"team_member--body\">\r\n                <h4 class=\"team_member--name\">技术研发</h4>\r\n                <span class=\"team_member--position\">专业的研发团队，深耕AI算力调度与优化</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"am-u-sm-12 am-u-md-4\">\r\n            <div class=\"team-box\">\r\n              <div class=\"our-team-img\">\r\n                <img src=\"images/yunwei.png\" alt=\"运维团队\" loading=\"lazy\">\r\n              </div>\r\n              <div class=\"team_member--body\">\r\n                <h4 class=\"team_member--name\">运维保障</h4>\r\n                <span class=\"team_member--position\">5x8小时专业运维，确保服务稳定可靠</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"am-u-sm-12 am-u-md-4\">\r\n            <div class=\"team-box\">\r\n              <div class=\"our-team-img\">\r\n                <img src=\"images/khjl.png\" alt=\"客服团队\" loading=\"lazy\">\r\n              </div>\r\n              <div class=\"team_member--body\">\r\n                <h4 class=\"team_member--name\">客户服务</h4>\r\n                <span class=\"team_member--position\">专业客户经理，提供一对一贴心服务</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n\r\n    <!-- 联系我们 -->\r\n    <section class=\"contact-section\">\r\n      <div class=\"container\">\r\n        <div class=\"am-g\">\r\n          <div class=\"am-u-md-4\">\r\n            <div class=\"contact-item\">\r\n              <i class=\"am-icon-phone\"></i>\r\n              <h4>联系电话</h4>\r\n              <p>13913283376</p>\r\n            </div>\r\n          </div>\r\n          <div class=\"am-u-md-4\">\r\n            <div class=\"contact-item\">\r\n              <i class=\"am-icon-envelope\"></i>\r\n              <h4>官方公众号</h4>\r\n              <p>昆山新质创新数字技术研究院</p>\r\n            </div>\r\n          </div>\r\n          <div class=\"am-u-md-4\">\r\n            <div class=\"contact-item\">\r\n              <i class=\"am-icon-map-marker\"></i>\r\n              <h4>公司地址</h4>\r\n              <p>江苏省苏州市昆山市玉山镇祖冲之路1699号昆山工业技术研究院综合南楼1404</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n\r\n    <!-- 立即开始 -->\r\n    <section class=\"cta-section\">\r\n      <div class=\"cta-content\">\r\n        <h2>连接智算未来，让高性能计算像水电一样可得、可控、可负担</h2>\r\n        <div class=\"cta-buttons\">\r\n          <button class=\"am-btn am-btn-primary\" @click=\"startTrial\">立即开始</button>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  </Layout>\r\n</template>\r\n\r\n<script>\r\nimport Layout from \"@/components/common/Layout\";\r\n\r\nexport default {\r\n  name: 'AboutView',\r\n  components: { Layout },\r\n  data() {\r\n    return {\r\n      connectionLines: []\r\n    }\r\n  },\r\n  mounted() {\r\n    this.generateConnectionLines();\r\n    this.startCounterAnimation();\r\n    this.optimizeForMobile();\r\n    this.handleOrientationChange();\r\n    this.optimizeForLargeScreen();\r\n  },\r\n  methods: {\r\n    startTrial() {\r\n      this.$router.push('/product');\r\n    },\r\n    contactUs() {\r\n      // 可以添加联系我们的逻辑\r\n      console.log('联系我们');\r\n    },\r\n    getParticleStyle() {\r\n      return {\r\n        left: Math.random() * 100 + '%',\r\n        top: Math.random() * 100 + '%',\r\n        animationDelay: Math.random() * 10 + 's',\r\n        animationDuration: (Math.random() * 20 + 10) + 's'\r\n      };\r\n    },\r\n    getNodeStyle() {\r\n      return {\r\n        left: Math.random() * 90 + 5 + '%',\r\n        top: Math.random() * 90 + 5 + '%',\r\n        animationDelay: Math.random() * 3 + 's'\r\n      };\r\n    },\r\n    generateConnectionLines() {\r\n      const lines = [];\r\n      for (let i = 0; i < 15; i++) {\r\n        lines.push({\r\n          id: i,\r\n          x1: Math.random() * 100,\r\n          y1: Math.random() * 100,\r\n          x2: Math.random() * 100,\r\n          y2: Math.random() * 100\r\n        });\r\n      }\r\n      this.connectionLines = lines;\r\n    },\r\n    startCounterAnimation() {\r\n      const counters = document.querySelectorAll('.stat-number');\r\n      counters.forEach(counter => {\r\n        const target = parseInt(counter.getAttribute('data-target'));\r\n        const increment = target / 100;\r\n        let current = 0;\r\n\r\n        const timer = setInterval(() => {\r\n          current += increment;\r\n          if (current >= target) {\r\n            counter.textContent = target;\r\n            clearInterval(timer);\r\n          } else {\r\n            counter.textContent = Math.floor(current);\r\n          }\r\n        }, 50);\r\n      });\r\n    },\r\n\r\n    // 移动端优化\r\n    optimizeForMobile() {\r\n      // 检测是否为移动设备\r\n      const isMobile = window.innerWidth <= 768;\r\n\r\n      if (isMobile) {\r\n        // 减少动画复杂度\r\n        this.reduceMobileAnimations();\r\n\r\n        // 优化触摸事件\r\n        this.optimizeTouchEvents();\r\n\r\n        // 预加载关键图片\r\n        this.preloadCriticalImages();\r\n      }\r\n    },\r\n\r\n    reduceMobileAnimations() {\r\n      // 减少粒子数量\r\n      const particles = document.querySelectorAll('.particle');\r\n      particles.forEach((particle, index) => {\r\n        if (index > 20) { // 只保留前20个粒子\r\n          particle.style.display = 'none';\r\n        }\r\n      });\r\n\r\n      // 简化数据流动画\r\n      const dataStreams = document.querySelectorAll('.data-stream');\r\n      dataStreams.forEach((stream, index) => {\r\n        if (index > 4) { // 只保留前4个数据流\r\n          stream.style.display = 'none';\r\n        }\r\n      });\r\n    },\r\n\r\n    optimizeTouchEvents() {\r\n      // 为移动端优化触摸反馈\r\n      const touchElements = document.querySelectorAll('.our_mission--item, .team-box, .contact-item, .am-btn');\r\n\r\n      touchElements.forEach(element => {\r\n        element.addEventListener('touchstart', () => {\r\n          element.style.transform = 'translateY(-2px)';\r\n        }, { passive: true });\r\n\r\n        element.addEventListener('touchend', () => {\r\n          setTimeout(() => {\r\n            element.style.transform = '';\r\n          }, 150);\r\n        }, { passive: true });\r\n      });\r\n    },\r\n\r\n    preloadCriticalImages() {\r\n      const criticalImages = [\r\n        '/images/tiangonghead.jpeg',\r\n        '/images/back1.webp'\r\n      ];\r\n\r\n      criticalImages.forEach(src => {\r\n        const img = new Image();\r\n        img.src = src;\r\n      });\r\n    },\r\n\r\n    handleOrientationChange() {\r\n      // 处理屏幕方向变化\r\n      window.addEventListener('orientationchange', () => {\r\n        setTimeout(() => {\r\n          // 重新计算布局\r\n          this.generateConnectionLines();\r\n\r\n          // 重新优化移动端设置\r\n          this.optimizeForMobile();\r\n\r\n          // 重新优化大屏设置\r\n          this.optimizeForLargeScreen();\r\n        }, 500);\r\n      });\r\n    },\r\n\r\n    // 大屏优化\r\n    optimizeForLargeScreen() {\r\n      const isLargeScreen = window.innerWidth >= 1920;\r\n\r\n      if (isLargeScreen) {\r\n        // 为大屏添加特殊类名\r\n        document.body.classList.add('large-screen-mode');\r\n\r\n        // 优化背景GIF显示\r\n        this.optimizeBackgroundGif();\r\n\r\n        // 调整动画效果\r\n        this.enhanceAnimationsForLargeScreen();\r\n      } else {\r\n        document.body.classList.remove('large-screen-mode');\r\n      }\r\n    },\r\n\r\n    optimizeBackgroundGif() {\r\n      const bannerBg = document.querySelector('.about-banner-bg');\r\n      if (bannerBg && window.innerWidth >= 1920) {\r\n        // 在大屏上使用contain确保GIF完整显示\r\n        bannerBg.style.backgroundSize = 'contain';\r\n        bannerBg.style.backgroundRepeat = 'no-repeat';\r\n        bannerBg.style.backgroundPosition = 'center';\r\n      }\r\n    },\r\n\r\n    enhanceAnimationsForLargeScreen() {\r\n      // 在大屏上增强动画效果\r\n      if (window.innerWidth >= 1920) {\r\n        // 增加粒子数量\r\n        const particlesContainer = document.querySelector('.particles-container');\r\n        if (particlesContainer && particlesContainer.children.length < 80) {\r\n          for (let i = 50; i < 80; i++) {\r\n            const particle = document.createElement('div');\r\n            particle.className = 'particle';\r\n            particle.style.cssText = `\r\n              left: ${Math.random() * 100}%;\r\n              top: ${Math.random() * 100}%;\r\n              animation-delay: ${Math.random() * 10}s;\r\n              animation-duration: ${Math.random() * 20 + 10}s;\r\n            `;\r\n            particlesContainer.appendChild(particle);\r\n          }\r\n        }\r\n      }\r\n    }\r\n  },\r\n\r\n  beforeDestroy() {\r\n    // 清理大屏模式类名\r\n    document.body.classList.remove('large-screen-mode');\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n/* 顶部Banner */\r\n.about-banner {\r\n  background:\r\n    radial-gradient(ellipse at top, rgba(20, 112, 255, 0.3) 0%, transparent 70%),\r\n    radial-gradient(ellipse at bottom, rgba(74, 144, 255, 0.2) 0%, transparent 70%),\r\n    linear-gradient(135deg, #0a0a0a 0%, #1470FF 30%, #4A90FF 50%, #1470FF 70%, #0a0a0a 100%);\r\n  color: white;\r\n  padding: 200px 0 100px;\r\n  text-align: center;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.about-banner-bg {\r\n  pointer-events: none;\r\n}\r\n\r\n/* 动态网格背景 */\r\n/* .grid-background {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background-image:\r\n    linear-gradient(rgba(20, 112, 255, 0.1) 1px, transparent 1px),\r\n    linear-gradient(90deg, rgba(20, 112, 255, 0.1) 1px, transparent 1px);\r\n  background-size: 50px 50px;\r\n  animation: gridMove 20s linear infinite;\r\n  z-index: 1;\r\n}\r\n\r\n@keyframes gridMove {\r\n  0% { transform: translate(0, 0); }\r\n  100% { transform: translate(50px, 50px); }\r\n} */\r\n\r\n/* 粒子效果 */\r\n.particles-container {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  z-index: 2;\r\n}\r\n\r\n.particle {\r\n  position: absolute;\r\n  width: 2px;\r\n  height: 2px;\r\n  background: rgba(255, 255, 255, 0.8);\r\n  border-radius: 50%;\r\n  animation: particleFloat 15s linear infinite;\r\n}\r\n\r\n.particle:nth-child(3n) {\r\n  background: rgba(20, 112, 255, 0.8);\r\n  width: 3px;\r\n  height: 3px;\r\n}\r\n\r\n.particle:nth-child(5n) {\r\n  background: rgba(74, 144, 255, 0.6);\r\n  width: 1px;\r\n  height: 1px;\r\n}\r\n\r\n@keyframes particleFloat {\r\n  0% {\r\n    transform: translateY(100vh) translateX(0) scale(0);\r\n    opacity: 0;\r\n  }\r\n  10% {\r\n    opacity: 1;\r\n  }\r\n  90% {\r\n    opacity: 1;\r\n  }\r\n  100% {\r\n    transform: translateY(-100px) translateX(100px) scale(1);\r\n    opacity: 0;\r\n  }\r\n}\r\n\r\n\r\n/* 数据流效果 */\r\n.data-streams {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  z-index: 2;\r\n}\r\n\r\n.data-stream {\r\n  position: absolute;\r\n  width: 2px;\r\n  height: 100px;\r\n  background: linear-gradient(to bottom, transparent, rgba(20, 112, 255, 0.8), transparent);\r\n  animation: dataFlow 3s linear infinite;\r\n}\r\n\r\n.data-stream:nth-child(1) { left: 10%; animation-delay: 0s; }\r\n.data-stream:nth-child(2) { left: 25%; animation-delay: 0.5s; }\r\n.data-stream:nth-child(3) { left: 40%; animation-delay: 1s; }\r\n.data-stream:nth-child(4) { left: 55%; animation-delay: 1.5s; }\r\n.data-stream:nth-child(5) { left: 70%; animation-delay: 2s; }\r\n.data-stream:nth-child(6) { left: 85%; animation-delay: 2.5s; }\r\n.data-stream:nth-child(7) { left: 15%; animation-delay: 1.2s; }\r\n.data-stream:nth-child(8) { left: 75%; animation-delay: 0.8s; }\r\n\r\n@keyframes dataFlow {\r\n  0% {\r\n    transform: translateY(-100px);\r\n    opacity: 0;\r\n  }\r\n  50% {\r\n    opacity: 1;\r\n  }\r\n  100% {\r\n    transform: translateY(calc(100vh + 100px));\r\n    opacity: 0;\r\n  }\r\n}\r\n\r\n/* 算力节点连接线 */\r\n/* .network-nodes {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  z-index: 2;\r\n}\r\n\r\n.node {\r\n  position: absolute;\r\n  width: 8px;\r\n  height: 8px;\r\n  background: rgba(20, 112, 255, 0.8);\r\n  border-radius: 50%;\r\n  animation: nodePulse 2s ease-in-out infinite;\r\n}\r\n\r\n.node-pulse {\r\n  position: absolute;\r\n  top: -4px;\r\n  left: -4px;\r\n  width: 16px;\r\n  height: 16px;\r\n  border: 2px solid rgba(20, 112, 255, 0.4);\r\n  border-radius: 50%;\r\n  animation: pulseRing 2s ease-out infinite;\r\n}\r\n\r\n@keyframes nodePulse {\r\n  0%, 100% { transform: scale(1); opacity: 1; }\r\n  50% { transform: scale(1.2); opacity: 0.8; }\r\n}\r\n\r\n@keyframes pulseRing {\r\n  0% {\r\n    transform: scale(0.8);\r\n    opacity: 1;\r\n  }\r\n  100% {\r\n    transform: scale(2);\r\n    opacity: 0;\r\n  }\r\n}\r\n\r\n.connection-lines {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  pointer-events: none;\r\n}\r\n\r\n.connection-line {\r\n  stroke: rgba(20, 112, 255, 0.3);\r\n  stroke-width: 0.5;\r\n  animation: lineGlow 3s ease-in-out infinite alternate;\r\n}\r\n\r\n@keyframes lineGlow {\r\n  0% { stroke-opacity: 0.3; }\r\n  100% { stroke-opacity: 0.8; }\r\n} */\r\n\r\n/* 光效层 */\r\n.light-effects {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  z-index: 3;\r\n  pointer-events: none;\r\n}\r\n\r\n.light-beam {\r\n  position: absolute;\r\n  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);\r\n  animation: lightSweep 8s ease-in-out infinite;\r\n}\r\n\r\n.light-beam-1 {\r\n  top: 20%;\r\n  left: -100%;\r\n  width: 200%;\r\n  height: 2px;\r\n  animation-delay: 0s;\r\n}\r\n\r\n.light-beam-2 {\r\n  top: 60%;\r\n  left: -100%;\r\n  width: 200%;\r\n  height: 1px;\r\n  animation-delay: 2s;\r\n}\r\n\r\n.light-beam-3 {\r\n  top: 80%;\r\n  left: -100%;\r\n  width: 200%;\r\n  height: 3px;\r\n  animation-delay: 4s;\r\n}\r\n\r\n@keyframes lightSweep {\r\n  0% { transform: translateX(-100%); opacity: 0; }\r\n  50% { opacity: 1; }\r\n  100% { transform: translateX(100%); opacity: 0; }\r\n}\r\n\r\n.banner-content {\r\n  position: relative;\r\n  z-index: 10;\r\n  /* padding-top: 100px; */\r\n}\r\n\r\n/* 标题效果 */\r\n.banner-title {\r\n  font-size: 48px;\r\n  margin-bottom: 30px;\r\n  font-weight: 700;\r\n  line-height: 1.2;\r\n  position: relative;\r\n}\r\n\r\n.title-word {\r\n  display: inline-block;\r\n  position: relative;\r\n  background: linear-gradient(45deg, #ffffff, #8fb9fd, #ffffff);\r\n  background-size: 200% 200%;\r\n  -webkit-background-clip: text;\r\n  -webkit-text-fill-color: transparent;\r\n  background-clip: text;\r\n  animation: titleShimmer 3s ease-in-out infinite;\r\n}\r\n\r\n.title-word::before {\r\n  content: attr(data-text);\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  background: linear-gradient(45deg, transparent, rgba(255, 255, 255,1), transparent);\r\n  -webkit-background-clip: text;\r\n  -webkit-text-fill-color: transparent;\r\n  background-clip: text;\r\n  animation: textGlow 2s ease-in-out infinite alternate;\r\n}\r\n\r\n@keyframes titleShimmer {\r\n  0%, 100% { background-position: 0% 50%; }\r\n  50% { background-position: 100% 50%; }\r\n}\r\n\r\n@keyframes textGlow {\r\n  0% { opacity: 0; }\r\n  100% { opacity: 1; }\r\n}\r\n\r\n.title-separator {\r\n  color: rgba(255, 255, 255, 0.8);\r\n}\r\n\r\n/* 字幕打字效果 */\r\n.banner-subtitle-container {\r\n  margin-bottom: 130px;\r\n}\r\n\r\n.banner-subtitle {\r\n  font-size: 24px;\r\n  margin-bottom: 10px;\r\n  opacity: 0;\r\n  transform: translateY(20px);\r\n  max-width: 600px;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n}\r\n\r\n.typing-effect {\r\n  animation: typeIn 1s ease-out forwards;\r\n}\r\n\r\n@keyframes typeIn {\r\n  0% {\r\n    opacity: 0;\r\n    transform: translateY(20px);\r\n  }\r\n  100% {\r\n    opacity: 0.9;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n/* 关于我们部分 */\r\n.about-section {\r\n  padding: 60px 0;\r\n  background: linear-gradient(135deg, #f8f9fa 0%, #e6f2ff 100%);\r\n  position: relative;\r\n}\r\n\r\n/* 移动端文本优化 */\r\n.our-company-text h3 {\r\n  color: #333;\r\n  font-weight: 600;\r\n  margin-bottom: 25px;\r\n}\r\n\r\n.our-company-text p {\r\n  color: #555;\r\n  line-height: 1.7;\r\n  margin-bottom: 20px;\r\n  font-size: 16px;\r\n}\r\n\r\n/* 图片优化 */\r\n.our-company-img {\r\n  text-align: center;\r\n  margin-top: 20px;\r\n}\r\n\r\n.our-company-img img {\r\n  max-width: 100%;\r\n  height: auto;\r\n  border-radius: 15px;\r\n  box-shadow: 0 8px 25px rgba(20, 112, 255, 0.15);\r\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\r\n}\r\n\r\n.our-company-img img:hover {\r\n  transform: translateY(-5px);\r\n  box-shadow: 0 15px 35px rgba(20, 112, 255, 0.25);\r\n}\r\n\r\n.about-section::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"dots\" width=\"20\" height=\"20\" patternUnits=\"userSpaceOnUse\"><circle cx=\"10\" cy=\"10\" r=\"1\" fill=\"%23d4e8ff\" opacity=\"0.3\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23dots)\"/></svg>');\r\n  z-index: 1;\r\n}\r\n\r\n.about-section .container {\r\n  position: relative;\r\n  z-index: 2;\r\n}\r\n\r\n.container {\r\n  width: 95%;\r\n  max-width: 1170px;\r\n  margin: 0 auto;\r\n  padding: 0 15px;\r\n}\r\n\r\n/* 数据统计\r\n.stats-section {\r\n  background: linear-gradient(135deg, #f8f9fa 0%, #e6f2ff 100%);\r\n  padding: 60px 0;\r\n  position: relative;\r\n}\r\n\r\n.stats-section::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"grid\" width=\"10\" height=\"10\" patternUnits=\"userSpaceOnUse\"><path d=\"M 10 0 L 0 0 0 10\" fill=\"none\" stroke=\"%23d4e8ff\" stroke-width=\"0.5\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23grid)\"/></svg>');\r\n  opacity: 0.3;\r\n  z-index: 1;\r\n}\r\n\r\n.stats-section .container {\r\n  position: relative;\r\n  z-index: 2;\r\n}\r\n\r\n.stat-item {\r\n  text-align: center;\r\n  padding: 30px 15px;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.stat-item:hover {\r\n  transform: translateY(-5px);\r\n}\r\n\r\n.stat-number {\r\n  font-size: 48px;\r\n  font-weight: 700;\r\n  color: #1470FF;\r\n  margin-bottom: 10px;\r\n  text-shadow: 0 2px 4px rgba(20, 112, 255, 0.1);\r\n}\r\n\r\n.stat-label {\r\n  font-size: 16px;\r\n  color: #555;\r\n  font-weight: 500;\r\n} */\r\n\r\n/* 选择我们的理由 */\r\n.our-mission {\r\n  padding: 60px 0;\r\n  background: #fff;\r\n  position: relative;\r\n}\r\n\r\n.our-mission::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: linear-gradient(45deg, transparent 49%, rgba(20, 112, 255, 0.02) 50%, transparent 51%);\r\n  z-index: 1;\r\n}\r\n\r\n.our-mission .container {\r\n  position: relative;\r\n  z-index: 2;\r\n}\r\n\r\n.section--header {\r\n  text-align: center;\r\n  /* margin-bottom: 60px; */\r\n}\r\n\r\n.section--title {\r\n  font-size: 36px;\r\n  color: #333;\r\n  margin-bottom: 20px;\r\n  font-weight: 600;\r\n  position: relative;\r\n}\r\n\r\n.section--title::after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: -10px;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  width: 60px;\r\n  height: 3px;\r\n  background: linear-gradient(90deg, #1470FF, #4A90FF);\r\n  border-radius: 2px;\r\n}\r\n\r\n.section--description {\r\n  font-size: 18px;\r\n  color: #666;\r\n  max-width: 600px;\r\n  margin: 0 auto;\r\n  line-height: 1.6;\r\n}\r\n\r\n.our_mission--item {\r\n  text-align: center;\r\n  padding: 40px 20px;\r\n  transition: all 0.3s ease;\r\n  border-radius: 10px;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.our_mission--item::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: linear-gradient(135deg, rgba(20, 112, 255, 0.05) 0%, rgba(74, 144, 255, 0.05) 100%);\r\n  opacity: 0;\r\n  transition: opacity 0.3s ease;\r\n  z-index: 1;\r\n}\r\n\r\n.our_mission--item:hover::before {\r\n  opacity: 1;\r\n}\r\n\r\n.our_mission--item:hover {\r\n  transform: translateY(-8px);\r\n  box-shadow: 0 15px 35px rgba(20, 112, 255, 0.15);\r\n}\r\n\r\n.our_mission--item_media {\r\n  margin-bottom: 25px;\r\n  position: relative;\r\n  z-index: 2;\r\n}\r\n\r\n.our_mission--item_media i {\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.our_mission--item:hover .our_mission--item_media i {\r\n  color: #1470FF !important;\r\n  transform: scale(1.1);\r\n}\r\n\r\n.our_mission--item_title {\r\n  font-size: 20px;\r\n  color: #333;\r\n  margin-bottom: 15px;\r\n  font-weight: 600;\r\n  position: relative;\r\n  z-index: 2;\r\n}\r\n\r\n.our_mission--item_body {\r\n  position: relative;\r\n  z-index: 2;\r\n}\r\n\r\n.our_mission--item_body p {\r\n  font-size: 15px;\r\n  color: #666;\r\n  line-height: 1.6;\r\n  margin: 0;\r\n}\r\n\r\n/* 核心团队 */\r\n.our-team {\r\n  padding: 60px 0;\r\n  background: linear-gradient(135deg, #f8f9fa 0%, #e6f2ff 100%);\r\n  position: relative;\r\n}\r\n\r\n.our-team::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><circle cx=\"20\" cy=\"20\" r=\"2\" fill=\"%23d4e8ff\" opacity=\"0.5\"/><circle cx=\"80\" cy=\"20\" r=\"2\" fill=\"%23d4e8ff\" opacity=\"0.5\"/><circle cx=\"20\" cy=\"80\" r=\"2\" fill=\"%23d4e8ff\" opacity=\"0.5\"/><circle cx=\"80\" cy=\"80\" r=\"2\" fill=\"%23d4e8ff\" opacity=\"0.5\"/></svg>');\r\n  z-index: 1;\r\n}\r\n\r\n.our-team .container {\r\n  position: relative;\r\n  z-index: 2;\r\n}\r\n\r\n.team-box {\r\n  background: #fff;\r\n  border-radius: 15px;\r\n  overflow: hidden;\r\n  box-shadow: 0 8px 25px rgba(20, 112, 255, 0.1);\r\n  transition: all 0.3s ease;\r\n  margin-bottom: 30px;\r\n  border: 1px solid rgba(20, 112, 255, 0.1);\r\n}\r\n\r\n.team-box:hover {\r\n  transform: translateY(-8px);\r\n  box-shadow: 0 20px 40px rgba(20, 112, 255, 0.2);\r\n  border-color: rgba(20, 112, 255, 0.3);\r\n}\r\n\r\n.our-team-img {\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.our-team-img::after {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: linear-gradient(135deg, rgba(20, 112, 255, 0.1) 0%, rgba(74, 144, 255, 0.1) 100%);\r\n  opacity: 0;\r\n  transition: opacity 0.3s ease;\r\n}\r\n\r\n.team-box:hover .our-team-img::after {\r\n  opacity: 1;\r\n}\r\n\r\n.our-team-img img {\r\n  width: 100%;\r\n  height: 250px;\r\n  object-fit: cover;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.team-box:hover .our-team-img img {\r\n  transform: scale(1.05);\r\n}\r\n\r\n.team_member--body {\r\n  padding: 30px 25px;\r\n  text-align: center;\r\n  position: relative;\r\n}\r\n\r\n.team_member--name {\r\n  font-size: 20px;\r\n  color: #333;\r\n  margin-bottom: 10px;\r\n  font-weight: 600;\r\n}\r\n\r\n.team_member--position {\r\n  font-size: 15px;\r\n  color: #666;\r\n  line-height: 1.5;\r\n}\r\n\r\n/* 联系我们 */\r\n.contact-section {\r\n  padding: 20px 0;\r\n  background: #fff;\r\n  position: relative;\r\n}\r\n\r\n.contact-section::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: linear-gradient(45deg, transparent 49%, rgba(20, 112, 255, 0.02) 50%, transparent 51%);\r\n  z-index: 1;\r\n}\r\n\r\n.contact-section .container {\r\n  position: relative;\r\n  z-index: 2;\r\n}\r\n\r\n.contact-item {\r\n  text-align: center;\r\n  padding: 30px 20px;\r\n  transition: all 0.3s ease;\r\n  border-radius: 10px;\r\n  position: relative;\r\n}\r\n\r\n.contact-item::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: linear-gradient(135deg, rgba(20, 112, 255, 0.05) 0%, rgba(74, 144, 255, 0.05) 100%);\r\n  opacity: 0;\r\n  transition: opacity 0.3s ease;\r\n  border-radius: 10px;\r\n}\r\n\r\n.contact-item:hover::before {\r\n  opacity: 1;\r\n}\r\n\r\n.contact-item:hover {\r\n  transform: translateY(-5px);\r\n}\r\n\r\n.contact-item i {\r\n  font-size: 48px;\r\n  color: #1470FF;\r\n  /* margin-bottom: 20px; */\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.contact-item:hover i {\r\n  color: #4A90FF;\r\n  transform: scale(1.1);\r\n}\r\n\r\n.contact-item h4 {\r\n  font-size: 20px;\r\n  color: #333;\r\n  margin-bottom: 15px;\r\n  font-weight: 600;\r\n  position: relative;\r\n  z-index: 2;\r\n}\r\n\r\n.contact-item p {\r\n  font-size: 16px;\r\n  color: #666;\r\n  margin: 0;\r\n  position: relative;\r\n  z-index: 2;\r\n}\r\n\r\n/* 立即开始 */\r\n.cta-section {\r\n  background-image: url(\"../../assets/images/index/back3.png\");\r\n  width: 100%;\r\n  height: 120px;\r\n  color: white;\r\n  padding: 20px 0;\r\n  text-align: center;\r\n  position: relative;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.cta-content {\r\n  position: relative;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  width: 100%;\r\n  max-width: 1200px;\r\n  padding: 0 20px;\r\n}\r\n\r\n.cta-content h2 {\r\n  color: white;\r\n  font-size: 2rem;\r\n  font-weight: bold;\r\n  margin: 0;\r\n  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.cta-content p {\r\n  display: none; /* 隐藏描述文字，保持和首页一致 */\r\n}\r\n\r\n.cta-buttons {\r\n  display: flex;\r\n  justify-content: center;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.cta-buttons .am-btn {\r\n  background-color: white;\r\n  color: #0d47a1;\r\n  border: none;\r\n  border-radius: 25px;\r\n  padding: 10px 25px;\r\n  font-size: 1.7rem;\r\n  font-weight: 500;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  text-decoration: none;\r\n  display: inline-block;\r\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.am-btn-primary:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);\r\n  background-color: #0a69ff;\r\n  color: white;\r\n}\r\n\r\n/* 移动端适配 */\r\n@media screen and (max-width: 768px) {\r\n  .cta-section {\r\n    height: auto;\r\n    padding: 20px;\r\n  }\r\n\r\n  .cta-content {\r\n    flex-direction: column;\r\n    text-align: center;\r\n  }\r\n\r\n  .cta-content h2 {\r\n    font-size: 18px;\r\n    margin-bottom: 15px;\r\n  }\r\n\r\n  .cta-buttons .am-btn {\r\n    font-size: 16px;\r\n    padding: 10px 25px;\r\n  }\r\n}\r\n\r\n.am-btn:focus,\r\n.am-btn:active {\r\n  outline: none;\r\n  box-shadow: none;\r\n}\r\n\r\n/* 响应式设计 */\r\n/* 平板设备 */\r\n@media (max-width: 1024px) {\r\n  .banner-title {\r\n    font-size: 40px;\r\n  }\r\n\r\n  .banner-subtitle {\r\n    font-size: 20px;\r\n  }\r\n\r\n  .about-banner {\r\n    padding: 100px 0 70px;\r\n  }\r\n\r\n  .section--title {\r\n    font-size: 32px;\r\n  }\r\n\r\n  .our_mission--item_media i {\r\n    font-size: 42px !important;\r\n  }\r\n\r\n  .contact-item i {\r\n    font-size: 42px;\r\n  }\r\n}\r\n\r\n/* 移动端 - 大屏手机 */\r\n@media (max-width: 768px) {\r\n  .container {\r\n    width: 100%;\r\n    padding: 0 20px;\r\n  }\r\n\r\n  .banner-title {\r\n    font-size: 32px;\r\n    line-height: 1.3;\r\n    margin-bottom: 25px;\r\n  }\r\n\r\n  .banner-subtitle {\r\n    font-size: 18px;\r\n    margin-bottom: 8px;\r\n  }\r\n\r\n  .about-banner {\r\n    padding: 80px 0 60px;\r\n    min-height: 70vh;\r\n  }\r\n\r\n  .banner-subtitle-container {\r\n    margin-bottom: 30px;\r\n  }\r\n\r\n  /* 优化动画效果性能 */\r\n  .particle {\r\n    display: none; /* 移动端隐藏粒子效果以提升性能 */\r\n  }\r\n\r\n  .data-stream {\r\n    width: 1px;\r\n    height: 50px;\r\n  }\r\n\r\n  /* .node {\r\n    width: 6px;\r\n    height: 6px;\r\n  } */\r\n\r\n  /* .node-pulse {\r\n    width: 12px;\r\n    height: 12px;\r\n    top: -3px;\r\n    left: -3px;\r\n  } */\r\n\r\n  /* 减少光效以提升性能 */\r\n  .light-beam {\r\n    display: none;\r\n  }\r\n\r\n  /* 各个区块的移动端适配 */\r\n  .about-section,\r\n  .our-mission,\r\n  .our-team,\r\n  .contact-section {\r\n    padding: 50px 0;\r\n  }\r\n\r\n\r\n\r\n  .section--title {\r\n    font-size: 28px;\r\n    margin-bottom: 15px;\r\n  }\r\n\r\n  .section--description {\r\n    font-size: 16px;\r\n    padding: 0 10px;\r\n  }\r\n\r\n  /* 关于我们文本区域 */\r\n  .our-company-text {\r\n    margin-bottom: 30px;\r\n  }\r\n\r\n  .our-company-text h3 {\r\n    font-size: 24px;\r\n    margin-bottom: 20px;\r\n    text-align: center;\r\n  }\r\n\r\n  .our-company-text p {\r\n    font-size: 15px;\r\n    line-height: 1.6;\r\n    margin-bottom: 15px;\r\n    text-align: justify;\r\n  }\r\n\r\n  .our-company-img img {\r\n    max-width: 100%;\r\n    height: auto;\r\n    border-radius: 10px;\r\n  }\r\n\r\n  /* 选择我们的理由 */\r\n  .our_mission--item {\r\n    margin-bottom: 30px;\r\n    padding: 30px 15px;\r\n  }\r\n\r\n  .our_mission--item_media i {\r\n    font-size: 40px !important;\r\n  }\r\n\r\n  .our_mission--item_title {\r\n    font-size: 18px;\r\n    margin-bottom: 12px;\r\n  }\r\n\r\n  .our_mission--item_body p {\r\n    font-size: 14px;\r\n    line-height: 1.5;\r\n  }\r\n\r\n  /* 团队区域 */\r\n  .team-box {\r\n    margin-bottom: 25px;\r\n  }\r\n\r\n  .our-team-img img {\r\n    height: 200px;\r\n  }\r\n\r\n  .team_member--body {\r\n    padding: 20px 15px;\r\n  }\r\n\r\n  .team_member--name {\r\n    font-size: 18px;\r\n  }\r\n\r\n  .team_member--position {\r\n    font-size: 14px;\r\n  }\r\n\r\n  /* 联系我们 */\r\n  .contact-item {\r\n    padding: 30px 15px;\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .contact-item i {\r\n    font-size: 40px;\r\n    margin-bottom: 15px;\r\n  }\r\n\r\n  .contact-item h4 {\r\n    font-size: 18px;\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .contact-item p {\r\n    font-size: 15px;\r\n  }\r\n\r\n  /* CTA区域 */\r\n  .cta-content h2 {\r\n    font-size: 28px;\r\n    margin-bottom: 15px;\r\n  }\r\n\r\n  .cta-content p {\r\n    font-size: 16px;\r\n    margin-bottom: 30px;\r\n    padding: 0 10px;\r\n  }\r\n\r\n  .cta-buttons {\r\n    flex-direction: column;\r\n    align-items: center;\r\n    gap: 15px;\r\n  }\r\n\r\n  .cta-buttons .am-btn {\r\n    width: 220px;\r\n    padding: 12px 30px;\r\n    font-size: 15px;\r\n  }\r\n}\r\n\r\n/* 移动端 - 小屏手机 */\r\n@media (max-width: 480px) {\r\n  .container {\r\n    padding: 0 15px;\r\n  }\r\n\r\n  .banner-title {\r\n    font-size: 26px;\r\n    line-height: 1.2;\r\n  }\r\n\r\n  .banner-subtitle {\r\n    font-size: 16px;\r\n  }\r\n\r\n  .about-banner {\r\n    padding: 60px 0 40px;\r\n    min-height: 60vh;\r\n  }\r\n\r\n  .section--title {\r\n    font-size: 24px;\r\n  }\r\n\r\n  .section--description {\r\n    font-size: 15px;\r\n  }\r\n\r\n  /* 关于我们文本 */\r\n  .our-company-text h3 {\r\n    font-size: 22px;\r\n  }\r\n\r\n  .our-company-text p {\r\n    font-size: 14px;\r\n    line-height: 1.5;\r\n  }\r\n\r\n  /* 选择我们的理由 */\r\n  .our_mission--item {\r\n    padding: 25px 10px;\r\n  }\r\n\r\n  .our_mission--item_media i {\r\n    font-size: 36px !important;\r\n  }\r\n\r\n  .our_mission--item_title {\r\n    font-size: 16px;\r\n  }\r\n\r\n  .our_mission--item_body p {\r\n    font-size: 13px;\r\n  }\r\n\r\n  /* 团队区域 */\r\n  .our-team-img img {\r\n    height: 180px;\r\n  }\r\n\r\n  .team_member--body {\r\n    padding: 15px 10px;\r\n  }\r\n\r\n  .team_member--name {\r\n    font-size: 16px;\r\n  }\r\n\r\n  .team_member--position {\r\n    font-size: 13px;\r\n  }\r\n\r\n  /* 联系我们 */\r\n  .contact-item {\r\n    padding: 25px 10px;\r\n  }\r\n\r\n  .contact-item i {\r\n    font-size: 36px;\r\n  }\r\n\r\n  .contact-item h4 {\r\n    font-size: 16px;\r\n  }\r\n\r\n  .contact-item p {\r\n    font-size: 14px;\r\n  }\r\n\r\n  /* CTA区域 */\r\n  .cta-content h2 {\r\n    font-size: 24px;\r\n  }\r\n\r\n  .cta-content p {\r\n    font-size: 15px;\r\n    padding: 0 5px;\r\n  }\r\n\r\n  .cta-buttons .am-btn {\r\n    width: 200px;\r\n    padding: 10px 25px;\r\n    font-size: 14px;\r\n  }\r\n}\r\n\r\n/* 超小屏设备 */\r\n@media (max-width: 360px) {\r\n  .container {\r\n    padding: 0 10px;\r\n  }\r\n\r\n  .banner-title {\r\n    font-size: 22px;\r\n  }\r\n\r\n  .banner-subtitle {\r\n    font-size: 14px;\r\n  }\r\n\r\n  .section--title {\r\n    font-size: 20px;\r\n  }\r\n\r\n  .our_mission--item_media i {\r\n    font-size: 32px !important;\r\n  }\r\n\r\n  .contact-item i {\r\n    font-size: 32px;\r\n  }\r\n\r\n  .cta-buttons .am-btn {\r\n    width: 180px;\r\n    font-size: 13px;\r\n  }\r\n}\r\n\r\n/* 触摸设备优化 */\r\n@media (hover: none) and (pointer: coarse) {\r\n  .our_mission--item:hover,\r\n  .team-box:hover,\r\n  .contact-item:hover {\r\n    transform: none;\r\n  }\r\n\r\n  .our_mission--item:active,\r\n  .team-box:active,\r\n  .contact-item:active {\r\n    transform: translateY(-3px);\r\n    transition: transform 0.1s ease;\r\n  }\r\n\r\n  .cta-buttons .am-btn:hover {\r\n    transform: none;\r\n  }\r\n\r\n  .cta-buttons .am-btn:active {\r\n    transform: translateY(-2px) scale(0.98);\r\n    transition: transform 0.1s ease;\r\n  }\r\n}\r\n\r\n/* 横屏模式优化 */\r\n@media (max-width: 768px) and (orientation: landscape) {\r\n  .about-banner {\r\n    padding: 40px 0 30px;\r\n    min-height: 50vh;\r\n  }\r\n\r\n  .banner-title {\r\n    font-size: 28px;\r\n  }\r\n\r\n  .banner-subtitle {\r\n    font-size: 16px;\r\n  }\r\n\r\n  .about-section,\r\n  .our-mission,\r\n  .our-team,\r\n  .contact-section {\r\n    padding: 40px 0;\r\n  }\r\n}\r\n\r\n/* 移动端性能优化 */\r\n@media (max-width: 768px) {\r\n  /* 启用硬件加速 */\r\n  .about-banner,\r\n  .our_mission--item,\r\n  .team-box,\r\n  .contact-item,\r\n  .am-btn {\r\n    -webkit-transform: translateZ(0);\r\n    transform: translateZ(0);\r\n    -webkit-backface-visibility: hidden;\r\n    backface-visibility: hidden;\r\n  }\r\n\r\n  /* 优化滚动性能 */\r\n  .about-banner {\r\n    -webkit-overflow-scrolling: touch;\r\n  }\r\n\r\n  /* 减少重绘 */\r\n  .particle,\r\n  .data-stream,\r\n  .node {\r\n    will-change: transform;\r\n  }\r\n\r\n  /* 触摸优化 */\r\n  .our_mission--item,\r\n  .team-box,\r\n  .contact-item,\r\n  .am-btn {\r\n    -webkit-tap-highlight-color: rgba(20, 112, 255, 0.1);\r\n  }\r\n\r\n  /* 防止文本选择 */\r\n  .banner-title,\r\n  .section--title,\r\n  .our_mission--item_title,\r\n  .team_member--name,\r\n  .contact-item h4 {\r\n    -webkit-user-select: none;\r\n    -moz-user-select: none;\r\n    -ms-user-select: none;\r\n    user-select: none;\r\n  }\r\n}\r\n\r\n/* 高分辨率屏幕优化 */\r\n@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {\r\n  .our-company-img img,\r\n  .our-team-img img {\r\n    image-rendering: -webkit-optimize-contrast;\r\n    image-rendering: crisp-edges;\r\n  }\r\n}\r\n\r\n/* 减少动画的偏好设置支持 */\r\n@media (prefers-reduced-motion: reduce) {\r\n  .particle,\r\n  .data-stream,\r\n  .node,\r\n  .light-beam,\r\n  .typing-effect,\r\n  .title-word {\r\n    animation: none !important;\r\n  }\r\n\r\n  .our_mission--item:hover,\r\n  .team-box:hover,\r\n  .contact-item:hover,\r\n  .am-btn:hover {\r\n    transform: none !important;\r\n    transition: none !important;\r\n  }\r\n}\r\n\r\n/* 安卓大屏适配 - 简单优化 */\r\n@media (min-width: 1920px) {\r\n  /* 顶部Banner优化 - 确保全部显示 */\r\n  .about-banner {\r\n    padding: 120px 0 80px;\r\n    min-height: 90vh;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n  }\r\n\r\n  /* 背景GIF完整显示 */\r\n  .about-banner-bg {\r\n    background-size: contain !important;\r\n    background-repeat: no-repeat !important;\r\n    background-position: center !important;\r\n  }\r\n\r\n  /* 容器和间距调整 */\r\n  .container {\r\n    max-width: 1600px;\r\n    padding: 0 40px;\r\n  }\r\n\r\n  /* 标题字体适当增大 */\r\n  .banner-title {\r\n    font-size: 56px;\r\n    margin-bottom: 40px;\r\n  }\r\n\r\n  .banner-subtitle {\r\n    font-size: 26px;\r\n    margin-bottom: 12px;\r\n  }\r\n\r\n  /* 各区块间距调整 */\r\n  .about-section,\r\n  .our-mission,\r\n  .our-team {\r\n    padding: 80px 0;\r\n  }\r\n\r\n  .contact-section {\r\n    padding: 60px 0;\r\n  }\r\n\r\n  /* 盒子间距调整 */\r\n  .our_mission--item {\r\n    padding: 50px 30px;\r\n    margin-bottom: 40px;\r\n  }\r\n\r\n  /* 选择我们的理由 - 大屏布局优化 */\r\n  .our-mission .am-g {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    justify-content: space-between;\r\n    gap: 20px;\r\n  }\r\n\r\n  /* 默认1x4布局（一行四个） */\r\n  .our-mission .am-u-lg-3 {\r\n    flex: 0 0 calc(25% - 15px);\r\n    max-width: calc(25% - 15px);\r\n    margin-bottom: 0;\r\n  }\r\n\r\n  /* 如果屏幕宽度不够或者高度较小，使用2x2布局 */\r\n  @media (min-width: 1920px) and (max-width: 2200px) {\r\n    .our-mission .am-u-lg-3 {\r\n      flex: 0 0 calc(50% - 10px);\r\n      max-width: calc(50% - 10px);\r\n      margin-bottom: 20px;\r\n    }\r\n\r\n    .our-mission .am-u-lg-3:nth-child(3),\r\n    .our-mission .am-u-lg-3:nth-child(4) {\r\n      margin-bottom: 0;\r\n    }\r\n  }\r\n\r\n  .team-box {\r\n    margin-bottom: 40px;\r\n  }\r\n\r\n  .contact-item {\r\n    padding: 40px 25px;\r\n  }\r\n\r\n  /* 图标和文字适当增大 */\r\n  .our_mission--item_media i {\r\n    font-size: 56px !important;\r\n  }\r\n\r\n  .our_mission--item_title {\r\n    font-size: 22px;\r\n    margin-bottom: 18px;\r\n  }\r\n\r\n  .our_mission--item_body p {\r\n    font-size: 16px;\r\n    line-height: 1.7;\r\n  }\r\n\r\n  .section--title {\r\n    font-size: 42px;\r\n    margin-bottom: 25px;\r\n  }\r\n\r\n  .section--description {\r\n    font-size: 20px;\r\n    max-width: 700px;\r\n  }\r\n\r\n  /* 团队区域优化 */\r\n  .our-team-img img {\r\n    height: 280px;\r\n  }\r\n\r\n  .team_member--body {\r\n    padding: 35px 30px;\r\n  }\r\n\r\n  .team_member--name {\r\n    font-size: 22px;\r\n    margin-bottom: 12px;\r\n  }\r\n\r\n  .team_member--position {\r\n    font-size: 16px;\r\n    line-height: 1.6;\r\n  }\r\n\r\n  /* 联系我们区域优化 */\r\n  .contact-item i {\r\n    font-size: 56px;\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .contact-item h4 {\r\n    font-size: 22px;\r\n    margin-bottom: 16px;\r\n  }\r\n\r\n  .contact-item p {\r\n    font-size: 18px;\r\n    line-height: 1.6;\r\n  }\r\n\r\n  /* CTA区域优化 */\r\n  .cta-section {\r\n    height: 150px;\r\n    padding: 30px 0;\r\n  }\r\n\r\n  .cta-content {\r\n    max-width: 1400px;\r\n    padding: 0 40px;\r\n  }\r\n\r\n  .cta-content h2 {\r\n    font-size: 32px;\r\n  }\r\n\r\n  .cta-buttons .am-btn {\r\n    font-size: 20px;\r\n    padding: 15px 35px;\r\n    border-radius: 30px;\r\n  }\r\n}\r\n\r\n/* 超大屏优化 (2560px+) */\r\n@media (min-width: 2560px) {\r\n  .container {\r\n    max-width: 2000px;\r\n    padding: 0 60px;\r\n  }\r\n\r\n  .banner-title {\r\n    font-size: 68px;\r\n  }\r\n\r\n  .banner-subtitle {\r\n    font-size: 30px;\r\n  }\r\n\r\n  .section--title {\r\n    font-size: 48px;\r\n  }\r\n\r\n  .our_mission--item_media i {\r\n    font-size: 64px !important;\r\n  }\r\n}\r\n\r\n/* 大屏模式特殊优化 */\r\n.large-screen-mode .about-banner {\r\n  /* 确保Banner在大屏上完整显示 */\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.large-screen-mode .about-banner-bg {\r\n  /* 大屏上GIF完整显示 */\r\n  background-size: contain !important;\r\n  background-repeat: no-repeat !important;\r\n  background-position: center !important;\r\n}\r\n\r\n/* 安卓大屏横屏优化 */\r\n@media (min-width: 1920px) and (orientation: landscape) {\r\n  .about-banner {\r\n    padding: 80px 0 60px;\r\n  }\r\n\r\n  .banner-content {\r\n    width: 100%;\r\n    max-width: 1200px;\r\n  }\r\n\r\n  /* 横屏模式下可选择2x2布局（仅在屏幕高度较小时） */\r\n  @media (max-height: 1080px) {\r\n    .our-mission .am-u-lg-3 {\r\n      flex: 0 0 calc(50% - 15px);\r\n      max-width: calc(50% - 15px);\r\n    }\r\n  }\r\n\r\n  /* 团队区域横屏优化 */\r\n  .our-team .am-g {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    gap: 30px;\r\n  }\r\n\r\n  .our-team .am-u-md-4 {\r\n    flex: 1;\r\n  }\r\n\r\n  /* 联系我们横屏布局 */\r\n  .contact-section .am-g {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: stretch;\r\n    gap: 20px;\r\n  }\r\n\r\n  .contact-section .am-u-md-4 {\r\n    flex: 1;\r\n  }\r\n}\r\n</style>", "import mod from \"-!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./AboutView.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./AboutView.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./AboutView.vue?vue&type=template&id=2675a300&scoped=true&\"\nimport script from \"./AboutView.vue?vue&type=script&lang=js&\"\nexport * from \"./AboutView.vue?vue&type=script&lang=js&\"\nimport style0 from \"./AboutView.vue?vue&type=style&index=0&id=2675a300&prod&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"2675a300\",\n  null\n  \n)\n\nexport default component.exports"], "names": ["render", "_vm", "this", "_c", "_self", "staticClass", "_t", "staticRenderFns", "name", "components", "Footer", "<PERSON><PERSON>", "chatAi", "component", "_v", "on", "$event", "navigateTo", "_m", "attrs", "require", "staticStyle", "data", "methods", "path", "currentPath", "previousActivePath", "$nextTick", "navLinks", "document", "querySelectorAll", "for<PERSON>ach", "link", "classList", "contains", "add", "setTimeout", "remove", "$route", "window", "scrollTo", "top", "behavior", "$router", "go", "push", "_l", "n", "key", "style", "getParticleStyle", "startTrial", "Layout", "connectionLines", "mounted", "generateConnectionLines", "startCounterAnimation", "optimizeForMobile", "handleOrientationChange", "optimizeForLargeScreen", "contactUs", "console", "log", "left", "Math", "random", "animationDelay", "animationDuration", "getNodeStyle", "lines", "i", "id", "x1", "y1", "x2", "y2", "counters", "counter", "target", "parseInt", "getAttribute", "increment", "current", "timer", "setInterval", "textContent", "clearInterval", "floor", "isMobile", "innerWidth", "reduceMobileAnimations", "optimizeTouchEvents", "preloadCriticalImages", "particles", "particle", "index", "display", "dataStreams", "stream", "touchElements", "element", "addEventListener", "transform", "passive", "criticalImages", "src", "img", "Image", "isLargeScreen", "body", "optimizeBackgroundGif", "enhanceAnimationsForLargeScreen", "bannerBg", "querySelector", "backgroundSize", "backgroundRepeat", "backgroundPosition", "particlesContainer", "children", "length", "createElement", "className", "cssText", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "sourceRoot": ""}